[tool.poetry]
name = "blossom-data"
packages = [{ include = "blossom", from = "src" }]
version = "0.4.2"
description = "A simple way to synthesize LLM training data."
repository = "https://github.com/Azure99/BlossomData"
authors = ["Azure99"]
readme = "README.md"
license = "MIT"
keywords = ["llm", "data synthesis", "data processing", "pyspark", "ray", "machine learning", "nlp"]

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
pyyaml = "^6.0"
pydantic = "^2.0"
requests = "^2.0"
pillow = "^10.0"
pyspark = "^3.0"
ray = {version = "^2.0", extras = ["data"]}
pyarrow = ">=15.0.0"
numpy = "^2.0"

[tool.poetry.group.dev.dependencies]
black = "^24.10.0"
ruff = "^0.8.2"
mypy = "^1.13.0"
types-pyyaml = "^6.0.12.20240917"
types-requests = "^2.32.0.20241016"
pyarrow-stubs = "^17.19"

[tool.ruff]
target-version = "py39"

[tool.ruff.lint]
select = ["E", "F", "W", "B", "C4", "RUF", "ICN", "UP", "PL"]
ignore = ["E501", "PLR0913", "UP006", "UP035"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
