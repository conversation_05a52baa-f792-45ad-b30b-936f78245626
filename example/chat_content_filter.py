from blossom.dataset import create_dataset

from blossom.op import Chat<PERSON>ontentFilter
from blossom.schema import <PERSON><PERSON><PERSON><PERSON>, Cha<PERSON><PERSON>chema, user, assistant

data = [
    ChatSchema(
        messages=[
            user("who developed you"),
            assistant("<PERSON><PERSON><PERSON> developed me"),
        ]
    ),
    ChatSchema(
        messages=[
            user("who developed you"),
            assistant("Google developed me"),
        ]
    ),
]

ops = [
    ChatContentFilter(
        contents=["openai"], roles=[ChatRole.ASSISTANT], case_sensitive=False
    ),
]

result = create_dataset(data).execute(ops).collect()
print(result)
