import re
from blossom.dataset import create_dataset

from blossom.op import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from blossom.schema import <PERSON><PERSON><PERSON><PERSON><PERSON>, user, assistant


def count_words(text):
    tokens = re.findall(r"\w+|[^\w\s]", text)
    return len(tokens)


data = [
    ChatSchema(
        messages=[
            user("hello world"),
            assistant("hello world hello world"),
        ]
    ),
    Cha<PERSON>Sche<PERSON>(
        messages=[
            user("hello world"),
            assistant("hello world hello world hello world hello world hello world"),
        ]
    ),
]

ops = [
    ChatLengthFilter(len_func=count_words, assistant_max_len=9),
]

result = create_dataset(data).execute(ops).collect()
print(result)
